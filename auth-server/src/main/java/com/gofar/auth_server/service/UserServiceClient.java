package com.gofar.auth_server.service;

import com.gofar.auth_server.dto.EcoUserCredentials;
import com.gofar.auth_server.dto.ValidEcoUserDetails;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatusCode;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClient;

/**
 * Service client for communicating with the external user service
 */
@Service
public class UserServiceClient {

    private static final Logger logger = LoggerFactory.getLogger(UserServiceClient.class);
    
    private final RestClient userServiceRestClient;

    public UserServiceClient(RestClient userServiceRestClient) {
        this.userServiceRestClient = userServiceRestClient;
    }

    /**
     * Validates user credentials against the external user service
     *
     * @param email the user's email
     * @param password the user's password
     * @return ValidEcoUserDetails if credentials are valid, null otherwise
     */
    public ValidEcoUserDetails validateUser(String email, String password) {
        try {
            EcoUserCredentials credentials = new EcoUserCredentials(email, password);
            
            ValidEcoUserDetails response = userServiceRestClient
                    .post()
                    .uri("/api/v1/users/validate")
                    .body(credentials)
                    .retrieve()
                    .onStatus(HttpStatusCode::is4xxClientError, (request, response1) -> {
                        logger.warn("User validation failed for email: {} with status: {}", 
                                email, response1.getStatusCode());
                    })
                    .onStatus(HttpStatusCode::is5xxServerError, (request, response1) -> {
                        logger.error("User service error for email: {} with status: {}", 
                                email, response1.getStatusCode());
                        throw new RuntimeException("User service unavailable");
                    })
                    .body(ValidEcoUserDetails.class);
                    
            logger.debug("User validation successful for email: {}", email);
            return response;
            
        } catch (Exception e) {
            logger.error("Error validating user with email: {}", email, e);
            return null;
        }
    }
}
