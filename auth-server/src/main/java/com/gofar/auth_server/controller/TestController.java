package com.gofar.auth_server.controller;

import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * Test controller to verify authentication and authorization
 */
@RestController
@RequestMapping("/test")
public class TestController {

    @GetMapping("/user")
    public Map<String, Object> getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        
        return Map.of(
                "username", authentication.getName(),
                "authorities", authentication.getAuthorities(),
                "authenticated", authentication.isAuthenticated()
        );
    }

    @GetMapping("/public")
    public Map<String, String> publicEndpoint() {
        return Map.of("message", "This is a public endpoint");
    }

    @GetMapping("/debug")
    public Map<String, Object> debugInfo() {
        return Map.of(
                "authServerRunning", true,
                "timestamp", System.currentTimeMillis(),
                "userServiceUrl", "http://localhost:8081",
                "authorizationEndpoint", "/oauth2/v1/authorize",
                "tokenEndpoint", "/oauth2/v1/token"
        );
    }
}
