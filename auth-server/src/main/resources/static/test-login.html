<!DOCTYPE html>
<html>
<head>
    <title>OAuth2 Authorization Code Flow Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 600px; margin: 0 auto; }
        .button { 
            background-color: #4CAF50; 
            color: white; 
            padding: 10px 20px; 
            text-decoration: none; 
            border-radius: 4px; 
            display: inline-block; 
            margin: 10px 0;
        }
        .info { background-color: #f0f0f0; padding: 15px; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>OAuth2 Authorization Code Flow Test</h1>
        
        <div class="info">
            <h3>Test the Authorization Code Flow</h3>
            <p>Click the button below to initiate the OAuth2 authorization code flow:</p>
            <a href="/oauth2/v1/authorize?response_type=code&client_id=eco-frontend&redirect_uri=http://localhost:4200/callback&scope=openid user" 
               class="button">Start Authorization Flow</a>
        </div>
        
        <div class="info">
            <h3>Test Credentials</h3>
            <p>Use these credentials to test the login:</p>
            <ul>
                <li><strong>Email:</strong> <EMAIL></li>
                <li><strong>Password:</strong> password123</li>
            </ul>
            <p><em>Note: Make sure you have a user with these credentials in your user service database.</em></p>
        </div>
        
        <div class="info">
            <h3>Direct Login Test</h3>
            <p>Test the authentication directly:</p>
            <a href="/test/user" class="button">Test Protected Endpoint</a>
            <a href="/test/public" class="button">Test Public Endpoint</a>
            <a href="/test/debug" class="button">Debug Info</a>
        </div>

        <div class="info">
            <h3>OAuth2 Endpoints</h3>
            <p>Test individual OAuth2 endpoints:</p>
            <a href="/.well-known/openid-configuration" class="button">OpenID Configuration</a>
            <a href="/oauth2/v1/jwks" class="button">JWKS Endpoint</a>
        </div>
    </div>
</body>
</html>
